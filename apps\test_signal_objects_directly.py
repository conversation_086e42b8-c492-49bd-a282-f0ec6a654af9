#!/usr/bin/env python3
"""
Test setting wire numbers directly on signal objects using signal IDs

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_signal_objects_directly():
    """Test setting wire numbers on signal objects using signal IDs"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        signal = job.CreateSignalObject()
        
        print("Successfully connected to E3!")
        
        # Get signal IDs
        signal_ids_result = job.GetSignalIds()
        print(f"GetSignalIds result: {signal_ids_result}")
        
        if isinstance(signal_ids_result, tuple) and len(signal_ids_result) >= 2:
            signal_count = signal_ids_result[0]
            signal_ids = signal_ids_result[1]
            
            if isinstance(signal_ids, tuple):
                actual_signals = [sid for sid in signal_ids if sid is not None]
            else:
                actual_signals = [signal_ids] if signal_ids is not None else []
            
            print(f"Found {len(actual_signals)} signals")
            
            if actual_signals:
                # Test first few signals
                for i, signal_id in enumerate(actual_signals[:5]):
                    print(f"\n=== Testing signal {i+1}: ID {signal_id} ===")
                    
                    try:
                        signal.SetId(signal_id)
                        
                        # Get signal name
                        signal_name = signal.GetName()
                        print(f"Signal name: '{signal_name}'")
                        
                        # Check if signal has attributes
                        signal_attr_ids = signal.GetAttributeIds()
                        print(f"Signal GetAttributeIds: {signal_attr_ids}")
                        
                        # Try to get current wire number
                        current_wire_number = signal.GetAttributeValue("Wire number")
                        print(f"Current signal wire number: '{current_wire_number}'")
                        
                        # Try to set wire number
                        test_wire_num = f"SIGNAL_{signal_id}_TEST"
                        signal_set_result = signal.SetAttributeValue("Wire number", test_wire_num)
                        print(f"Signal SetAttributeValue result: {signal_set_result}")
                        
                        # Verify
                        new_signal_wire_number = signal.GetAttributeValue("Wire number")
                        print(f"New signal wire number: '{new_signal_wire_number}'")
                        
                        if new_signal_wire_number == test_wire_num:
                            print("✓ SUCCESS: Wire number set on signal object!")
                            
                            # Test if this affects connections with this signal
                            print("Testing if connections are affected...")
                            
                            # Find connections with this signal
                            connection = job.CreateConnectionObject()
                            connection_ids_result = job.GetAllConnectionIds()
                            
                            if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                                connection_ids = connection_ids_result[1]
                                if isinstance(connection_ids, tuple):
                                    actual_connections = [cid for cid in connection_ids if cid is not None]
                                else:
                                    actual_connections = [connection_ids] if connection_ids is not None else []
                                
                                # Check first few connections
                                for conn_id in actual_connections[:10]:
                                    try:
                                        connection.SetId(conn_id)
                                        conn_signal_name = connection.GetSignalName()
                                        
                                        if conn_signal_name == signal_name:
                                            conn_wire_number = connection.GetAttributeValue("Wire number")
                                            print(f"  Connection {conn_id} (signal '{conn_signal_name}') wire number: '{conn_wire_number}'")
                                            
                                            if conn_wire_number == test_wire_num:
                                                print("  ✓ Connection wire number matches signal!")
                                                return True
                                            
                                    except Exception as e:
                                        pass  # Skip errors for individual connections
                            
                            return True
                            
                        elif signal_set_result == 0:
                            print("? PARTIAL: SetAttributeValue returned 0 but value didn't change")
                        else:
                            print("✗ FAILED: SetAttributeValue returned non-zero")
                            
                    except Exception as e:
                        print(f"Error testing signal {signal_id}: {e}")
                
                # Test with different attribute names
                print(f"\n=== Testing different attribute names on signal {actual_signals[0]} ===")
                signal.SetId(actual_signals[0])
                signal_name = signal.GetName()
                
                wire_attributes = [
                    "Wire number",
                    "WIRE_NUMBER", 
                    "WireNumber",
                    "Label",
                    "Name",
                    "Designation",
                    "Reference"
                ]
                
                for attr in wire_attributes:
                    try:
                        current_val = signal.GetAttributeValue(attr)
                        test_val = f"TEST_{attr.replace(' ', '_')}"
                        set_result = signal.SetAttributeValue(attr, test_val)
                        new_val = signal.GetAttributeValue(attr)
                        
                        if new_val == test_val:
                            print(f"✓ SUCCESS: '{attr}' works on signals!")
                            return True
                        elif set_result == 0:
                            print(f"? PARTIAL: '{attr}' - Set: {set_result}, but no change")
                        else:
                            print(f"- FAILED: '{attr}' - Set: {set_result}")
                            
                    except Exception as e:
                        print(f"✗ ERROR: '{attr}' - {e}")
            
        print("\nSignal object test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Direct Signal Object Test")
    print("============================")
    success = test_signal_objects_directly()
    if success:
        print("\n✓ Found working method for signal wire numbers!")
    else:
        print("\n✗ No working method found for signal wire numbers.")
