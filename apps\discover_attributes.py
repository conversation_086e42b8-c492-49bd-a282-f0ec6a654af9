#!/usr/bin/env python3
"""
Discover available attributes in E3 connections

This script helps discover what attributes are available on E3 connections
to find the correct wire number attribute name.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def discover_connection_attributes():
    """Discover available attributes on E3 connections"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs (E3 returns (count, tuple_of_ids))
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        print(f"Found {len(actual_connections)} connections")
        
        # Test first few connections to discover attributes
        for i, conn_id in enumerate(actual_connections[:5]):
            print(f"\n=== Connection {conn_id} ===")
            
            try:
                connection.SetId(conn_id)
                signal_name = connection.GetSignalName()
                print(f"Signal: {signal_name}")
                
                # Try to discover available attributes by testing common ones
                print("Testing common attribute names:")
                
                test_attributes = [
                    "Wire number", "WIRE_NUMBER", "WireNumber", "Wire_Number",
                    "Wire", "WIRE", "WireNo", "WIRENO", "Wire_No", "WIRE_NO",
                    "Number", "NUMBER", "Num", "NUM", "No", "NO",
                    "Label", "LABEL", "Name", "NAME", "Text", "TEXT",
                    "Description", "DESCRIPTION", "Desc", "DESC",
                    "Reference", "REFERENCE", "Ref", "REF",
                    "Tag", "TAG", "ID", "Id", "Identifier", "IDENTIFIER"
                ]
                
                for attr_name in test_attributes:
                    try:
                        # Try to get the attribute value first
                        value = connection.GetAttributeValue(attr_name)
                        print(f"  ✓ {attr_name}: '{value}'")
                    except Exception as e:
                        # If getting fails, the attribute doesn't exist
                        continue
                
                # Try to get all attributes if there's a method for it
                print("\nTrying to get all attributes...")
                try:
                    # Some E3 versions have GetAllAttributeNames or similar
                    all_attrs = connection.GetAllAttributeNames()
                    if all_attrs:
                        print(f"All attributes: {all_attrs}")
                except Exception as e:
                    print(f"GetAllAttributeNames not available: {e}")
                
                try:
                    # Try GetAttributeNames
                    attr_names = connection.GetAttributeNames()
                    if attr_names:
                        print(f"Attribute names: {attr_names}")
                except Exception as e:
                    print(f"GetAttributeNames not available: {e}")
                
                # Try some other methods to discover attributes
                try:
                    # Try to get attribute count
                    attr_count = connection.GetAttributeCount()
                    print(f"Attribute count: {attr_count}")
                    
                    # Try to get attributes by index
                    if attr_count and attr_count > 0:
                        print("Attributes by index:")
                        for idx in range(min(attr_count, 10)):  # Limit to first 10
                            try:
                                attr_name = connection.GetAttributeName(idx)
                                attr_value = connection.GetAttributeValue(attr_name)
                                print(f"  [{idx}] {attr_name}: '{attr_value}'")
                            except Exception as e:
                                print(f"  [{idx}] Error: {e}")
                                
                except Exception as e:
                    print(f"Attribute enumeration not available: {e}")
                
                # Try some wire-specific methods
                print("\nTrying wire-specific methods:")
                try:
                    wire_name = connection.GetWireName()
                    print(f"GetWireName(): '{wire_name}'")
                except Exception as e:
                    print(f"GetWireName() not available: {e}")
                
                try:
                    wire_number = connection.GetWireNumber()
                    print(f"GetWireNumber(): '{wire_number}'")
                except Exception as e:
                    print(f"GetWireNumber() not available: {e}")
                
                try:
                    wire_id = connection.GetWireId()
                    print(f"GetWireId(): '{wire_id}'")
                except Exception as e:
                    print(f"GetWireId() not available: {e}")
                
            except Exception as e:
                print(f"Error processing connection {conn_id}: {e}")
        
        print("\nAttribute discovery completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Connection Attribute Discovery")
    print("=" * 40)
    
    discover_connection_attributes()

if __name__ == "__main__":
    main()
