#!/usr/bin/env python3
"""
Debug connection attribute setting issues

This script focuses on understanding why SetAttributeValue returns success
but doesn't actually set the value.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def debug_connection_attributes():
    """Debug connection attribute setting"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        print(f"Found {len(actual_connections)} connections")
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nTesting connection {test_conn_id}")
        
        try:
            connection.SetId(test_conn_id)
            signal_name = connection.GetSignalName()
            print(f"Signal: {signal_name}")
            
            # Check if we need to start an edit session
            print("\n=== Testing with edit session ===")
            try:
                print("Starting edit session...")
                edit_result = job.StartEdit()
                print(f"StartEdit result: {edit_result}")
                
                # Now try setting the attribute
                print("Setting 'Wire number' in edit mode...")
                set_result = connection.SetAttributeValue("Wire number", "EDIT123")
                print(f"SetAttributeValue result: {set_result}")
                
                # Check the value
                verify_value = connection.GetAttributeValue("Wire number")
                print(f"Value after setting: '{verify_value}'")
                
                if verify_value == "EDIT123":
                    print("✓ SUCCESS: Wire number was set in edit mode!")
                    
                    # Try setting a different value
                    connection.SetAttributeValue("Wire number", "EDIT456")
                    verify_value2 = connection.GetAttributeValue("Wire number")
                    print(f"After second set: '{verify_value2}'")
                    
                else:
                    print("✗ FAILED: Still not working in edit mode")
                
                # End edit session
                print("Ending edit session...")
                end_result = job.EndEdit()
                print(f"EndEdit result: {end_result}")
                
                # Check if value persists
                final_value = connection.GetAttributeValue("Wire number")
                print(f"Value after ending edit: '{final_value}'")
                
            except Exception as e:
                print(f"Edit session failed: {e}")
                try:
                    job.EndEdit()  # Clean up
                except:
                    pass
            
            # Test other attribute names that might work
            print("\n=== Testing alternative attribute names ===")
            test_attributes = [
                ("Wire number", "ALT1"),
                ("WIRE_NUMBER", "ALT2"), 
                ("WireNumber", "ALT3"),
                ("Wire_Number", "ALT4"),
                ("Wire", "ALT5"),
                ("Number", "ALT6"),
                ("Label", "ALT7"),
                ("Name", "ALT8"),
                ("Text", "ALT9")
            ]
            
            for attr_name, test_value in test_attributes:
                try:
                    print(f"\nTesting '{attr_name}'...")
                    
                    # Get current value
                    current = connection.GetAttributeValue(attr_name)
                    print(f"  Current value: '{current}'")
                    
                    # Try to set
                    result = connection.SetAttributeValue(attr_name, test_value)
                    print(f"  SetAttributeValue result: {result}")
                    
                    # Verify
                    new_value = connection.GetAttributeValue(attr_name)
                    print(f"  New value: '{new_value}'")
                    
                    if new_value == test_value:
                        print(f"  ✓ SUCCESS with '{attr_name}'!")
                        
                        # Clean up - set back to empty
                        connection.SetAttributeValue(attr_name, "")
                        break
                    else:
                        print(f"  ✗ Failed to set '{attr_name}'")
                        
                except Exception as e:
                    print(f"  Error with '{attr_name}': {e}")
            
            # Try checking if connection is locked or read-only
            print("\n=== Checking connection properties ===")
            try:
                # Check various connection properties that might indicate why it's read-only
                props_to_check = [
                    "IsLocked", "IsReadOnly", "IsEditable", "CanEdit",
                    "Status", "State", "Mode", "Type"
                ]
                
                for prop in props_to_check:
                    try:
                        value = getattr(connection, prop, None)
                        if value is not None:
                            if callable(value):
                                result = value()
                                print(f"  {prop}(): {result}")
                            else:
                                print(f"  {prop}: {value}")
                    except Exception as e:
                        print(f"  {prop}: Not available ({e})")
                        
            except Exception as e:
                print(f"Property checking failed: {e}")
                
        except Exception as e:
            print(f"Error processing connection {test_conn_id}: {e}")
        
        print("\nConnection attribute debugging completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Connection Attribute Debug")
    print("=" * 30)
    
    debug_connection_attributes()

if __name__ == "__main__":
    main()
