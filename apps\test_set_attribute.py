#!/usr/bin/env python3
"""
Test setting wire number attribute on E3 connections

This script tests setting the "Wire number" attribute to verify it works.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_set_wire_number():
    """Test setting wire number attribute"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs (E3 returns (count, tuple_of_ids))
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        print(f"Found {len(actual_connections)} connections")
        
        # Test setting wire number on first connection
        test_conn_id = actual_connections[0]
        print(f"\nTesting connection {test_conn_id}")
        
        try:
            connection.SetId(test_conn_id)
            signal_name = connection.GetSignalName()
            print(f"Signal: {signal_name}")
            
            # Get current wire number
            current_value = connection.GetAttributeValue("Wire number")
            print(f"Current 'Wire number': '{current_value}'")
            
            # Try to set wire number
            test_value = "TEST123"
            print(f"Attempting to set 'Wire number' to '{test_value}'...")
            
            try:
                result = connection.SetAttributeValue("Wire number", test_value)
                print(f"SetAttributeValue result: {result}")
                
                # Verify it was set
                new_value = connection.GetAttributeValue("Wire number")
                print(f"New 'Wire number': '{new_value}'")
                
                if new_value == test_value:
                    print("✓ SUCCESS: Wire number was set correctly!")
                else:
                    print(f"✗ FAILED: Expected '{test_value}', got '{new_value}'")
                
                # Try to set it back to empty
                print("Setting back to empty...")
                connection.SetAttributeValue("Wire number", "")
                final_value = connection.GetAttributeValue("Wire number")
                print(f"Final 'Wire number': '{final_value}'")
                
            except Exception as e:
                print(f"✗ FAILED to set attribute: {e}")
                print(f"Error type: {type(e)}")
                
                # Try alternative attribute names
                print("\nTrying alternative attribute names...")
                alternatives = ["WIRE_NUMBER", "WireNumber", "Wire_Number", "Wire"]
                
                for alt_name in alternatives:
                    try:
                        print(f"Trying '{alt_name}'...")
                        connection.SetAttributeValue(alt_name, test_value)
                        verify_value = connection.GetAttributeValue(alt_name)
                        if verify_value == test_value:
                            print(f"✓ SUCCESS with '{alt_name}'!")
                            # Clean up
                            connection.SetAttributeValue(alt_name, "")
                            break
                        else:
                            print(f"Set but value mismatch: '{verify_value}'")
                    except Exception as alt_e:
                        print(f"Failed with '{alt_name}': {alt_e}")
                
        except Exception as e:
            print(f"Error processing connection {test_conn_id}: {e}")
        
        print("\nAttribute setting test completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Wire Number Attribute Test")
    print("=" * 30)
    
    test_set_wire_number()

if __name__ == "__main__":
    main()
