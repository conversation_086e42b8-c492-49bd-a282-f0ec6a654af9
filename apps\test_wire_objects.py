#!/usr/bin/env python3
"""
Test working with E3 wire objects instead of connection objects

This script explores using wire objects to set wire numbers.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_wire_objects():
    """Test working with wire objects"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        # Try to create wire object
        try:
            wire = job.CreateWireObject()
            print("Successfully created wire object!")
        except Exception as e:
            print(f"Could not create wire object: {e}")
            wire = None
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        print(f"Found {len(actual_connections)} connections")
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nTesting connection {test_conn_id}")
        
        try:
            connection.SetId(test_conn_id)
            signal_name = connection.GetSignalName()
            print(f"Signal: {signal_name}")
            
            # Try to get wire ID from connection
            try:
                wire_id = connection.GetWireId()
                print(f"Wire ID: {wire_id}")
                
                if wire and wire_id:
                    print(f"Testing wire object with ID {wire_id}")
                    wire.SetId(wire_id)
                    
                    # Try wire-specific methods
                    try:
                        wire_name = wire.GetName()
                        print(f"Wire name: '{wire_name}'")
                    except Exception as e:
                        print(f"GetName failed: {e}")
                    
                    try:
                        wire_number = wire.GetNumber()
                        print(f"Wire number: '{wire_number}'")
                    except Exception as e:
                        print(f"GetNumber failed: {e}")
                    
                    # Try to set wire number
                    try:
                        print("Attempting to set wire number...")
                        result = wire.SetNumber("TEST456")
                        print(f"SetNumber result: {result}")
                        
                        # Verify
                        new_number = wire.GetNumber()
                        print(f"New wire number: '{new_number}'")
                        
                    except Exception as e:
                        print(f"SetNumber failed: {e}")
                    
                    # Try wire attributes
                    try:
                        wire_attr = wire.GetAttributeValue("Wire number")
                        print(f"Wire 'Wire number' attribute: '{wire_attr}'")
                        
                        # Try to set it
                        wire.SetAttributeValue("Wire number", "TEST789")
                        verify_attr = wire.GetAttributeValue("Wire number")
                        print(f"After setting: '{verify_attr}'")
                        
                    except Exception as e:
                        print(f"Wire attribute operations failed: {e}")
                        
            except Exception as e:
                print(f"GetWireId failed: {e}")
            
            # Try different approaches to modify connection
            print("\nTrying alternative connection methods...")
            
            # Try to see if connection needs to be in edit mode
            try:
                # Some E3 operations require starting an edit session
                print("Trying to start edit session...")
                job.StartEdit()
                
                # Now try setting the attribute
                result = connection.SetAttributeValue("Wire number", "EDIT_TEST")
                print(f"SetAttributeValue in edit mode result: {result}")
                
                verify_value = connection.GetAttributeValue("Wire number")
                print(f"Value after edit mode set: '{verify_value}'")
                
                # End edit session
                job.EndEdit()
                print("Edit session ended")
                
            except Exception as e:
                print(f"Edit session approach failed: {e}")
                try:
                    job.EndEdit()  # Try to clean up
                except:
                    pass
            
            # Try using different connection methods
            try:
                print("Trying SetName method...")
                result = connection.SetName("TEST_NAME")
                print(f"SetName result: {result}")
                
                name = connection.GetName()
                print(f"Connection name: '{name}'")
                
            except Exception as e:
                print(f"SetName failed: {e}")
                
        except Exception as e:
            print(f"Error processing connection {test_conn_id}: {e}")
        
        print("\nWire object test completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Wire Object Test")
    print("=" * 20)
    
    test_wire_objects()

if __name__ == "__main__":
    main()
