#!/usr/bin/env python3
"""
Test all possible wire number attribute names on connections

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_all_wire_attributes():
    """Test various wire number attribute names"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            connection_ids = connection_ids_result[1]
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print("No connections found")
            return
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nTesting connection {test_conn_id}")
        
        connection.SetId(test_conn_id)
        signal_name = connection.GetSignalName()
        print(f"Signal: {signal_name}")
        
        # List of possible wire number attribute names to test
        wire_attributes = [
            "Wire number",
            "WIRE_NUMBER", 
            "WireNumber",
            "Wire_Number",
            "WIRENUMBER",
            "Wire",
            "Number",
            "WireNo",
            "WIRENO",
            "Wire_No",
            "WireId",
            "WIREID",
            "Wire_Id",
            "Label",
            "LABEL",
            "Name",
            "NAME",
            "Designation",
            "DESIGNATION",
            "Reference",
            "REFERENCE",
            "Tag",
            "TAG",
            "ID",
            "Id",
            "Identifier",
            "IDENTIFIER",
            "Code",
            "CODE",
            "WireName",
            "WIRENAME",
            "Wire_Name",
            "SignalName",
            "SIGNALNAME",
            "Signal_Name",
            "NetName",
            "NETNAME",
            "Net_Name",
            "CoreNumber",
            "CORENUMBER",
            "Core_Number",
            "CoreName",
            "CORENAME",
            "Core_Name"
        ]
        
        print(f"\n=== Testing {len(wire_attributes)} possible attribute names ===")
        
        successful_attributes = []
        
        for attr in wire_attributes:
            try:
                # Check if attribute exists
                has_attr = connection.HasAttribute(attr)
                
                # Get current value
                current_val = connection.GetAttributeValue(attr)
                
                # Try to set a test value
                test_val = f"TEST_{attr.replace(' ', '_').upper()}"
                set_result = connection.SetAttributeValue(attr, test_val)
                
                # Verify the set
                new_val = connection.GetAttributeValue(attr)
                
                # Check if it worked
                if new_val == test_val:
                    print(f"✓ SUCCESS: '{attr}' - HasAttr: {has_attr}, Set: {set_result}, Value: '{new_val}'")
                    successful_attributes.append(attr)
                    
                    # Reset the value to what it was
                    connection.SetAttributeValue(attr, current_val)
                    
                elif set_result == 0 and new_val != test_val:
                    print(f"? PARTIAL: '{attr}' - HasAttr: {has_attr}, Set: {set_result}, but value didn't change")
                    
                elif has_attr == 1 or current_val != "":
                    print(f"- EXISTS: '{attr}' - HasAttr: {has_attr}, Current: '{current_val}', Set: {set_result}")
                    
            except Exception as e:
                print(f"✗ ERROR: '{attr}' - {e}")
        
        print(f"\n=== Summary ===")
        if successful_attributes:
            print(f"✓ Found {len(successful_attributes)} working attribute(s):")
            for attr in successful_attributes:
                print(f"  - {attr}")
        else:
            print("✗ No working attributes found")
        
        # Also test getting all attribute IDs to see what's actually available
        print(f"\n=== All available attributes on this connection ===")
        try:
            attr_ids_result = connection.GetAttributeIds()
            print(f"GetAttributeIds result: {attr_ids_result}")
            
            if isinstance(attr_ids_result, tuple) and len(attr_ids_result) >= 2:
                attr_count = attr_ids_result[0]
                attr_ids = attr_ids_result[1]
                
                print(f"Found {attr_count} attributes")
                
                if isinstance(attr_ids, tuple):
                    actual_attrs = [aid for aid in attr_ids if aid is not None]
                else:
                    actual_attrs = [attr_ids] if attr_ids is not None else []
                
                print(f"Attribute IDs: {actual_attrs}")
                
                # Try to get the names of these attributes
                for attr_id in actual_attrs[:10]:  # Limit to first 10
                    try:
                        # There might be a way to get attribute name from ID
                        # For now, just show the ID
                        attr_value = connection.GetAttributeValue(str(attr_id))
                        print(f"  Attribute {attr_id}: '{attr_value}'")
                    except:
                        pass
                        
        except Exception as e:
            print(f"GetAttributeIds failed: {e}")
        
        print("\nAttribute test completed!")
        return len(successful_attributes) > 0
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Wire Attribute Test")
    print("======================")
    success = test_all_wire_attributes()
    if success:
        print("\n✓ Found working attribute name(s)!")
    else:
        print("\n✗ No working attribute names found.")
