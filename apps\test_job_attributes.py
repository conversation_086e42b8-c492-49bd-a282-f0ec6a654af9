#!/usr/bin/env python3
"""
Test setting attributes on the job object itself

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_job_attributes():
    """Test setting attributes on job object"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        print("\n=== Testing job object attributes ===")
        
        # Check if job has attributes
        try:
            job_attr_ids = job.GetAttributeIds()
            print(f"Job GetAttributeIds: {job_attr_ids}")
        except Exception as e:
            print(f"Job GetAttributeIds failed: {e}")
        
        # Test setting wire number on job
        try:
            current_wire_number = job.GetAttributeValue("Wire number")
            print(f"Current job wire number: '{current_wire_number}'")
            
            test_wire_num = "JOB_WIRE_TEST_123"
            job_set_result = job.SetAttributeValue("Wire number", test_wire_num)
            print(f"Job SetAttributeValue result: {job_set_result}")
            
            new_job_wire_number = job.GetAttributeValue("Wire number")
            print(f"New job wire number: '{new_job_wire_number}'")
            
            if new_job_wire_number == test_wire_num:
                print("✓ SUCCESS: Wire number set on job object!")
                return True
            else:
                print("✗ FAILED: Wire number not set on job")
                
        except Exception as e:
            print(f"Job wire number test failed: {e}")
        
        # Test the GSS_PARENT attribute that works in your code
        print("\n=== Testing GSS_PARENT attribute (known to work) ===")
        try:
            current_gss = job.GetAttributeValue("GSS_PARENT")
            print(f"Current GSS_PARENT: '{current_gss}'")
            
            test_gss = "TEST_GSS_123"
            gss_set_result = job.SetAttributeValue("GSS_PARENT", test_gss)
            print(f"GSS_PARENT SetAttributeValue result: {gss_set_result}")
            
            new_gss = job.GetAttributeValue("GSS_PARENT")
            print(f"New GSS_PARENT: '{new_gss}'")
            
            if new_gss == test_gss:
                print("✓ SUCCESS: GSS_PARENT works on job object!")
                
                # Reset it back
                job.SetAttributeValue("GSS_PARENT", current_gss)
                print(f"Reset GSS_PARENT back to: '{current_gss}'")
                
            else:
                print("✗ FAILED: GSS_PARENT not set on job")
                
        except Exception as e:
            print(f"GSS_PARENT test failed: {e}")
        
        # Test other possible wire-related attributes on job
        print("\n=== Testing other wire attributes on job ===")
        wire_attributes = [
            "Wire number",
            "WIRE_NUMBER", 
            "WireNumber",
            "DefaultWire",
            "DEFAULT_WIRE",
            "GeneratedWireNameFormat",
            "GENERATED_WIRE_NAME_FORMAT",
            "NextWireNumber",
            "NEXT_WIRE_NUMBER"
        ]
        
        for attr in wire_attributes:
            try:
                current_val = job.GetAttributeValue(attr)
                test_val = f"TEST_{attr.replace(' ', '_').upper()}"
                set_result = job.SetAttributeValue(attr, test_val)
                new_val = job.GetAttributeValue(attr)
                
                if new_val == test_val:
                    print(f"✓ SUCCESS: '{attr}' works on job!")
                    # Reset it back
                    job.SetAttributeValue(attr, current_val)
                    return True
                elif set_result == 0 and new_val != test_val:
                    print(f"? PARTIAL: '{attr}' - Set: {set_result}, but no change")
                elif current_val != "":
                    print(f"- EXISTS: '{attr}' - Current: '{current_val}', Set: {set_result}")
                    
            except Exception as e:
                print(f"✗ ERROR: '{attr}' - {e}")
        
        print("\nJob attribute test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Job Attribute Test")
    print("=====================")
    success = test_job_attributes()
    if success:
        print("\n✓ Found working method for job attributes!")
    else:
        print("\n✗ No working method found for job attributes.")
