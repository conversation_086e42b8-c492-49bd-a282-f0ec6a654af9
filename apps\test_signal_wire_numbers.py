#!/usr/bin/env python3
"""
Test setting wire numbers on signal objects instead of connection objects

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_signal_wire_numbers():
    """Test setting wire numbers on signals"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        signal = job.CreateSignalObject()
        
        print("Successfully connected to E3!")
        
        # Get connection IDs to find signals
        connection_ids_result = job.GetAllConnectionIds()
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            connection_ids = connection_ids_result[1]
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print("No connections found")
            return False
        
        # Get unique signals from connections
        print("\n=== Finding unique signals ===")
        signals_found = {}
        
        for conn_id in actual_connections[:10]:  # Test first 10 connections
            try:
                connection.SetId(conn_id)
                signal_name = connection.GetSignalName()
                
                if signal_name and signal_name not in signals_found:
                    # Try to get the signal ID
                    try:
                        # Get net ID from connection (signals might be accessed through nets)
                        net_id = connection.GetNetId()
                        if net_id:
                            signals_found[signal_name] = {
                                'net_id': net_id,
                                'connection_id': conn_id
                            }
                            print(f"Signal '{signal_name}' -> Net ID: {net_id}")
                    except Exception as e:
                        print(f"Error getting net ID for signal '{signal_name}': {e}")
                        
            except Exception as e:
                print(f"Error processing connection {conn_id}: {e}")
        
        print(f"Found {len(signals_found)} unique signals")
        
        # Test setting wire numbers on signals through net objects
        if signals_found:
            print("\n=== Testing wire numbers on net objects ===")
            
            # Try to create a net object
            try:
                net = job.CreateNetObject()
                print("Net object created successfully")
                
                # Test first signal
                signal_name, signal_info = list(signals_found.items())[0]
                net_id = signal_info['net_id']
                
                print(f"Testing signal '{signal_name}' with net ID {net_id}")
                
                try:
                    net.SetId(net_id)
                    
                    # Check if net has attributes
                    net_attr_ids = net.GetAttributeIds()
                    print(f"Net GetAttributeIds: {net_attr_ids}")
                    
                    # Try to get/set wire number on net
                    current_wire_number = net.GetAttributeValue("Wire number")
                    print(f"Current net wire number: '{current_wire_number}'")
                    
                    # Try to set wire number
                    test_wire_num = "NET_WIRE_123"
                    net_set_result = net.SetAttributeValue("Wire number", test_wire_num)
                    print(f"Net SetAttributeValue result: {net_set_result}")
                    
                    # Verify
                    new_net_wire_number = net.GetAttributeValue("Wire number")
                    print(f"New net wire number: '{new_net_wire_number}'")
                    
                    if new_net_wire_number == test_wire_num:
                        print("✓ SUCCESS: Wire number set on net object!")
                        
                        # Test if this affects the connections
                        print("Testing if connection wire numbers are affected...")
                        connection.SetId(signal_info['connection_id'])
                        conn_wire_number = connection.GetAttributeValue("Wire number")
                        print(f"Connection wire number after net change: '{conn_wire_number}'")
                        
                        return True
                    else:
                        print("✗ FAILED: Wire number not set on net")
                        
                except Exception as e:
                    print(f"Error working with net {net_id}: {e}")
                    
            except Exception as e:
                print(f"Error creating net object: {e}")
        
        # Try direct signal object approach
        print("\n=== Testing direct signal object approach ===")
        
        # Check if we can get signal IDs directly
        try:
            # Look for signal-related methods on job
            job_methods = [attr for attr in dir(job) if not attr.startswith('_')]
            signal_methods = [m for m in job_methods if 'signal' in m.lower()]
            print(f"Signal-related methods on job: {signal_methods}")
            
            # Try some signal methods
            for method in signal_methods:
                if method.startswith('Get') and 'Id' in method:
                    try:
                        result = getattr(job, method)()
                        print(f"{method}: {result}")
                    except Exception as e:
                        print(f"{method} failed: {e}")
            
        except Exception as e:
            print(f"Error exploring signal methods: {e}")
        
        print("\nSignal wire number test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Signal Wire Number Test")
    print("==========================")
    success = test_signal_wire_numbers()
    if success:
        print("\n✓ Found working method for signal wire numbers!")
    else:
        print("\n✗ No working method found for signal wire numbers.")
