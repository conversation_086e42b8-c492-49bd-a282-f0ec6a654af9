#!/usr/bin/env python3
"""
Explore what object creation methods are available on the job object

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def explore_job_methods():
    """Explore job object methods"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Get all methods on the job object
        all_methods = [attr for attr in dir(job) if not attr.startswith('_')]
        
        # Filter for Create methods
        create_methods = [m for m in all_methods if m.startswith('Create')]
        get_methods = [m for m in all_methods if m.startswith('Get')]
        
        print(f"\n=== Create methods ({len(create_methods)}) ===")
        for method in sorted(create_methods):
            print(f"  {method}")
        
        print(f"\n=== Get methods ({len(get_methods)}) ===")
        for method in sorted(get_methods):
            print(f"  {method}")
        
        # Test some specific methods that might be relevant
        print(f"\n=== Testing specific methods ===")
        
        # Check if there are any wire-related get methods
        wire_get_methods = [m for m in get_methods if 'wire' in m.lower()]
        print(f"Wire-related get methods: {wire_get_methods}")
        
        # Check if there are any core-related methods
        core_methods = [m for m in all_methods if 'core' in m.lower()]
        print(f"Core-related methods: {core_methods}")
        
        # Check if there are any net-related methods
        net_methods = [m for m in all_methods if 'net' in m.lower()]
        print(f"Net-related methods: {net_methods}")
        
        # Try to see if there are any methods that might handle wire numbering
        number_methods = [m for m in all_methods if 'number' in m.lower()]
        print(f"Number-related methods: {number_methods}")
        
        print("\nJob method exploration completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("E3 Job Method Explorer")
    print("======================")
    explore_job_methods()
