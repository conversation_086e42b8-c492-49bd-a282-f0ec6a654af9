#!/usr/bin/env python3
"""
Test E3's built-in wire numbering system

This script explores E3's built-in wire numbering methods.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_wire_numbering_system():
    """Test E3's wire numbering system"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Test wire numbering methods
        print("\n=== Testing wire numbering methods ===")
        
        try:
            # Get next wire number
            next_wire = job.GetNextWireNumber()
            print(f"GetNextWireNumber: {next_wire}")
            
            # Handle tuple return
            if isinstance(next_wire, tuple):
                print(f"GetNextWireNumber returned tuple: {next_wire}")
                if len(next_wire) >= 2:
                    wire_number = next_wire[1]
                    print(f"Wire number from tuple: {wire_number}")
            
        except Exception as e:
            print(f"GetNextWireNumber failed: {e}")
        
        try:
            # Get next formatted wire number
            next_formatted = job.GetNextWireNumberFormatted()
            print(f"GetNextWireNumberFormatted: {next_formatted}")
            
            # Handle tuple return
            if isinstance(next_formatted, tuple):
                print(f"GetNextWireNumberFormatted returned tuple: {next_formatted}")
                if len(next_formatted) >= 2:
                    formatted_number = next_formatted[1]
                    print(f"Formatted number from tuple: {formatted_number}")
            
        except Exception as e:
            print(f"GetNextWireNumberFormatted failed: {e}")
        
        try:
            # Get wire name format
            wire_format = job.GetGeneratedWireNameFormat()
            print(f"GetGeneratedWireNameFormat: {wire_format}")
            
            # Handle tuple return
            if isinstance(wire_format, tuple):
                print(f"GetGeneratedWireNameFormat returned tuple: {wire_format}")
                if len(wire_format) >= 2:
                    format_string = wire_format[1]
                    print(f"Format string from tuple: {format_string}")
            
        except Exception as e:
            print(f"GetGeneratedWireNameFormat failed: {e}")
        
        # Test with connections
        print("\n=== Testing with connections ===")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
            
            print(f"Found {len(actual_connections)} connections")
            
            if actual_connections:
                connection = job.CreateConnectionObject()
                test_conn_id = actual_connections[0]
                connection.SetId(test_conn_id)
                
                signal_name = connection.GetSignalName()
                print(f"Testing connection {test_conn_id}, signal: {signal_name}")
                
                # Check if connection has any wire number already
                current_wire_number = connection.GetAttributeValue("Wire number")
                print(f"Current wire number: '{current_wire_number}'")
                
                # Try to see if there are cores associated with this connection
                try:
                    core_count_result = connection.GetCoreCount()
                    if isinstance(core_count_result, tuple):
                        core_count = core_count_result[0] if len(core_count_result) > 0 else 0
                    else:
                        core_count = core_count_result
                    
                    print(f"Connection has {core_count} cores")
                    
                    if core_count > 0:
                        # Get core IDs
                        core_ids_result = connection.GetCoreIds()
                        if isinstance(core_ids_result, tuple) and len(core_ids_result) >= 2:
                            core_ids = core_ids_result[1]
                            if isinstance(core_ids, tuple):
                                actual_cores = [cid for cid in core_ids if cid is not None]
                            else:
                                actual_cores = [core_ids] if core_ids is not None else []
                            
                            print(f"Core IDs: {actual_cores}")
                            
                            # Try to work with cores
                            if actual_cores:
                                # Check if we can create a core object
                                try:
                                    core = job.CreateCoreObject()
                                    print("Core object created successfully")
                                    
                                    test_core_id = actual_cores[0]
                                    core.SetId(test_core_id)
                                    
                                    # Try to get/set wire number on core
                                    core_wire_number = core.GetAttributeValue("Wire number")
                                    print(f"Core wire number: '{core_wire_number}'")
                                    
                                    # Try to set a wire number on the core
                                    test_wire_num = "TEST_CORE_123"
                                    core_set_result = core.SetAttributeValue("Wire number", test_wire_num)
                                    print(f"Core SetAttributeValue result: {core_set_result}")
                                    
                                    # Verify
                                    new_core_wire_number = core.GetAttributeValue("Wire number")
                                    print(f"New core wire number: '{new_core_wire_number}'")
                                    
                                    if new_core_wire_number == test_wire_num:
                                        print("✓ SUCCESS: Wire number set on core!")
                                    else:
                                        print("✗ FAILED: Wire number not set on core")
                                    
                                except Exception as e:
                                    print(f"Core object test failed: {e}")
                        
                except Exception as e:
                    print(f"Core count test failed: {e}")
        
        print("\nWire numbering system test completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("E3 Wire Numbering System Test")
    print("=============================")
    test_wire_numbering_system()
