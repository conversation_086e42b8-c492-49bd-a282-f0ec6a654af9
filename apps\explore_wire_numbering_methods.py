#!/usr/bin/env python3
"""
Explore E3's wire numbering system methods in detail

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def explore_wire_numbering_methods():
    """Explore wire numbering methods"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Get all methods on job that might be related to wire numbering
        job_methods = [attr for attr in dir(job) if not attr.startswith('_')]
        
        # Filter for wire-related methods
        wire_methods = [m for m in job_methods if 'wire' in m.lower()]
        number_methods = [m for m in job_methods if 'number' in m.lower()]
        
        print(f"\n=== Wire-related methods ({len(wire_methods)}) ===")
        for method in sorted(wire_methods):
            print(f"  {method}")
        
        print(f"\n=== Number-related methods ({len(number_methods)}) ===")
        for method in sorted(number_methods):
            print(f"  {method}")
        
        # Test specific wire numbering methods
        print(f"\n=== Testing wire numbering methods ===")
        
        # Test GetNextWireNumber
        try:
            next_wire = job.GetNextWireNumber()
            print(f"GetNextWireNumber: {next_wire} (type: {type(next_wire)})")
        except Exception as e:
            print(f"GetNextWireNumber failed: {e}")
        
        # Test GetNextWireNumberFormatted
        try:
            next_formatted = job.GetNextWireNumberFormatted()
            print(f"GetNextWireNumberFormatted: {next_formatted} (type: {type(next_formatted)})")
        except Exception as e:
            print(f"GetNextWireNumberFormatted failed: {e}")
        
        # Test FreeWireNumber
        try:
            # Try to free a wire number (this might not work without a valid number)
            free_result = job.FreeWireNumber(1)
            print(f"FreeWireNumber(1): {free_result}")
        except Exception as e:
            print(f"FreeWireNumber failed: {e}")
        
        # Test GetWireRange
        try:
            wire_range = job.GetWireRange()
            print(f"GetWireRange: {wire_range}")
        except Exception as e:
            print(f"GetWireRange failed: {e}")
        
        # Look for any Set methods related to wire numbering
        set_wire_methods = [m for m in job_methods if m.startswith('Set') and ('wire' in m.lower() or 'number' in m.lower())]
        print(f"\n=== Set methods for wire/number ({len(set_wire_methods)}) ===")
        for method in sorted(set_wire_methods):
            print(f"  {method}")
        
        # Test some Set methods
        for method in set_wire_methods:
            if 'Wire' in method:
                try:
                    print(f"\nTesting {method}...")
                    # Try calling with a test value
                    if 'Range' in method:
                        # Might need two parameters for range
                        result = getattr(job, method)(1, 100)
                    elif 'Format' in method:
                        # Might need a format string
                        result = getattr(job, method)("TEST_FORMAT")
                    else:
                        # Try with a single parameter
                        result = getattr(job, method)(1)
                    print(f"  {method} result: {result}")
                except Exception as e:
                    print(f"  {method} failed: {e}")
        
        # Look for any methods that might assign wire numbers to connections/signals
        assign_methods = [m for m in job_methods if 'assign' in m.lower() or 'apply' in m.lower()]
        print(f"\n=== Assign/Apply methods ({len(assign_methods)}) ===")
        for method in sorted(assign_methods):
            print(f"  {method}")
        
        # Look for any update methods
        update_methods = [m for m in job_methods if 'update' in m.lower()]
        print(f"\n=== Update methods ({len(update_methods)}) ===")
        for method in sorted(update_methods):
            print(f"  {method}")
        
        # Test UpdateCores method specifically
        if 'UpdateCores' in update_methods:
            try:
                print(f"\nTesting UpdateCores...")
                update_result = job.UpdateCores()
                print(f"UpdateCores result: {update_result}")
            except Exception as e:
                print(f"UpdateCores failed: {e}")
        
        print("\nWire numbering method exploration completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("E3 Wire Numbering Method Explorer")
    print("=================================")
    explore_wire_numbering_methods()
