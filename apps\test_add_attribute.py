#!/usr/bin/env python3
"""
Test using AddAttributeValue instead of SetAttributeValue

This script tests if AddAttributeV<PERSON>ue works for setting wire numbers.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_add_attribute():
    """Test AddAttributeValue method"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nTesting connection {test_conn_id}")
        
        connection.SetId(test_conn_id)
        signal_name = connection.GetSignalName()
        print(f"Signal: {signal_name}")
        
        # Test attribute operations
        attr_name = "Wire number"
        test_value = "ADD_TEST_123"
        
        print(f"\n=== Testing attribute '{attr_name}' ===")
        
        # Check if attribute exists
        try:
            has_attr = connection.HasAttribute(attr_name)
            print(f"HasAttribute('{attr_name}'): {has_attr}")
        except Exception as e:
            print(f"HasAttribute failed: {e}")
        
        # Get current value
        try:
            current_value = connection.GetAttributeValue(attr_name)
            print(f"Current value: '{current_value}'")
        except Exception as e:
            print(f"GetAttributeValue failed: {e}")
        
        # Try AddAttributeValue - but handle tuple returns!
        try:
            print(f"Trying AddAttributeValue('{attr_name}', '{test_value}')...")
            add_result = connection.AddAttributeValue(attr_name, test_value)
            print(f"AddAttributeValue result: {add_result}")

            # Handle tuple return from AddAttributeValue
            if isinstance(add_result, tuple):
                print(f"AddAttributeValue returned tuple: {add_result}")
                if len(add_result) >= 1:
                    actual_result = add_result[0]
                    print(f"Actual result from tuple: {actual_result}")

            # Check if it worked
            new_value = connection.GetAttributeValue(attr_name)
            print(f"Value after AddAttributeValue: '{new_value}'")

            if new_value == test_value:
                print("✓ SUCCESS with AddAttributeValue!")

                # Try to change it with SetAttributeValue now
                print("Now trying SetAttributeValue...")
                set_result = connection.SetAttributeValue(attr_name, "SET_TEST_456")
                print(f"SetAttributeValue result: {set_result}")
                verify_value = connection.GetAttributeValue(attr_name)
                print(f"After SetAttributeValue: '{verify_value}'")

            else:
                print("✗ AddAttributeValue didn't work")

        except Exception as e:
            print(f"AddAttributeValue failed: {e}")
        
        # Try with different attribute names
        print(f"\n=== Testing other attribute names ===")
        
        test_attrs = [
            "WIRE_NUMBER",
            "WireNumber", 
            "Wire_Number",
            "Wire",
            "Number",
            "Label"
        ]
        
        for attr in test_attrs:
            try:
                print(f"\nTesting '{attr}':")
                
                # Check if exists
                has_attr = connection.HasAttribute(attr)
                print(f"  HasAttribute: {has_attr}")
                
                # Get current
                current = connection.GetAttributeValue(attr)
                print(f"  Current: '{current}'")
                
                # Try to add - handle tuple returns
                test_val = f"TEST_{attr}"
                add_result = connection.AddAttributeValue(attr, test_val)
                print(f"  AddAttributeValue result: {add_result}")

                # Handle tuple return
                if isinstance(add_result, tuple):
                    print(f"  AddAttributeValue returned tuple: {add_result}")

                # Verify
                new_val = connection.GetAttributeValue(attr)
                print(f"  New value: '{new_val}'")
                
                if new_val == test_val:
                    print(f"  ✓ SUCCESS with '{attr}'!")
                    
                    # Clean up
                    try:
                        connection.DeleteAttribute(attr)
                        print(f"  Cleaned up '{attr}'")
                    except:
                        pass
                    break
                    
            except Exception as e:
                print(f"  Error with '{attr}': {e}")
        
        # Try creating a completely new attribute
        print(f"\n=== Testing custom attribute ===")
        custom_attr = "CustomWireNumber"
        custom_value = "CUSTOM_123"
        
        try:
            print(f"Creating custom attribute '{custom_attr}'...")
            
            has_custom = connection.HasAttribute(custom_attr)
            print(f"HasAttribute('{custom_attr}'): {has_custom}")
            
            add_result = connection.AddAttributeValue(custom_attr, custom_value)
            print(f"AddAttributeValue result: {add_result}")

            # Handle tuple return
            if isinstance(add_result, tuple):
                print(f"AddAttributeValue returned tuple: {add_result}")

            verify_custom = connection.GetAttributeValue(custom_attr)
            print(f"Custom attribute value: '{verify_custom}'")
            
            if verify_custom == custom_value:
                print("✓ SUCCESS with custom attribute!")
                
                # Try to modify it
                connection.SetAttributeValue(custom_attr, "MODIFIED_456")
                modified_value = connection.GetAttributeValue(custom_attr)
                print(f"After modification: '{modified_value}'")
                
                # Clean up
                connection.DeleteAttribute(custom_attr)
                print("Custom attribute cleaned up")
            
        except Exception as e:
            print(f"Custom attribute test failed: {e}")
        
        print("\nAddAttributeValue test completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 AddAttributeValue Test")
    print("=" * 25)
    
    test_add_attribute()

if __name__ == "__main__":
    main()
