#!/usr/bin/env python3
"""
Debug script for E3 API behavior

This script helps debug the exact format of data returned by E3 API methods.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def debug_e3_api():
    """Debug E3 API data formats"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        pin = job.CreatePinObject()
        sheet = job.CreateSheetObject()
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Debug connection IDs
        print("\n=== DEBUGGING CONNECTION IDS ===")
        connection_ids = job.GetAllConnectionIds()
        print(f"Type of connection_ids: {type(connection_ids)}")
        print(f"Length: {len(connection_ids) if hasattr(connection_ids, '__len__') else 'N/A'}")
        
        if isinstance(connection_ids, tuple):
            print("Connection IDs is a tuple")
            print(f"First 5 elements: {connection_ids[:5]}")
            print(f"Types of first 5 elements: {[type(x) for x in connection_ids[:5]]}")
        elif isinstance(connection_ids, list):
            print("Connection IDs is a list")
            print(f"First 5 elements: {connection_ids[:5]}")
        else:
            print(f"Connection IDs is: {connection_ids}")
        
        # Try to get a valid connection ID
        valid_conn_id = None
        if isinstance(connection_ids, (tuple, list)):
            for conn_id in connection_ids:
                if conn_id is not None and not isinstance(conn_id, tuple):
                    valid_conn_id = conn_id
                    break
                elif isinstance(conn_id, tuple):
                    for sub_id in conn_id:
                        if sub_id is not None:
                            valid_conn_id = sub_id
                            break
                    if valid_conn_id:
                        break
        else:
            valid_conn_id = connection_ids
        
        if valid_conn_id is None:
            print("Could not find a valid connection ID")
            return
        
        print(f"\nUsing connection ID: {valid_conn_id} (type: {type(valid_conn_id)})")
        
        # Debug connection details
        print("\n=== DEBUGGING CONNECTION DETAILS ===")
        try:
            connection.SetId(valid_conn_id)
            signal_name = connection.GetSignalName()
            print(f"Signal name: {signal_name}")
            
            # Debug pin IDs
            pin_ids = connection.GetPinIds()
            print(f"Type of pin_ids: {type(pin_ids)}")
            print(f"Pin IDs: {pin_ids}")
            
            if isinstance(pin_ids, tuple):
                print("Pin IDs is a tuple")
                print(f"Length: {len(pin_ids)}")
                print(f"First few elements: {pin_ids[:3] if len(pin_ids) > 3 else pin_ids}")
                print(f"Types of elements: {[type(x) for x in pin_ids[:3]]}")
            elif isinstance(pin_ids, list):
                print("Pin IDs is a list")
                print(f"Length: {len(pin_ids)}")
            else:
                print(f"Pin IDs is: {pin_ids}")
            
            # Try to get a valid pin ID
            valid_pin_id = None
            if isinstance(pin_ids, (tuple, list)):
                for pin_id in pin_ids:
                    if pin_id is not None and not isinstance(pin_id, tuple):
                        valid_pin_id = pin_id
                        break
                    elif isinstance(pin_id, tuple):
                        for sub_id in pin_id:
                            if sub_id is not None:
                                valid_pin_id = sub_id
                                break
                        if valid_pin_id:
                            break
            else:
                valid_pin_id = pin_ids
            
            if valid_pin_id is None:
                print("Could not find a valid pin ID")
                return
            
            print(f"\nUsing pin ID: {valid_pin_id} (type: {type(valid_pin_id)})")
            
            # Debug pin location
            print("\n=== DEBUGGING PIN LOCATION ===")
            try:
                pin.SetId(valid_pin_id)
                
                # Try different ways to call GetSchemaLocation
                print("Trying GetSchemaLocation()...")
                
                try:
                    result = pin.GetSchemaLocation()
                    print(f"GetSchemaLocation() returned: {result}")
                    print(f"Type: {type(result)}")
                    
                    if isinstance(result, tuple):
                        print(f"Tuple length: {len(result)}")
                        for i, item in enumerate(result):
                            print(f"  [{i}]: {item} (type: {type(item)})")
                    
                except Exception as e:
                    print(f"GetSchemaLocation() failed: {e}")
                
                # Try with parameters
                print("\nTrying GetSchemaLocation with parameters...")
                try:
                    import pythoncom
                    x = pythoncom.Empty
                    y = pythoncom.Empty
                    grid = pythoncom.Empty
                    col = pythoncom.Empty
                    row = pythoncom.Empty
                    
                    result2 = pin.GetSchemaLocation(x, y, grid, col, row)
                    print(f"GetSchemaLocation with params returned: {result2}")
                    print(f"x: {x}, y: {y}, grid: {grid}, col: {col}, row: {row}")
                    
                except Exception as e:
                    print(f"GetSchemaLocation with params failed: {e}")
                
                # Try to get sheet info if we have a sheet ID
                if isinstance(result, tuple) and len(result) > 0:
                    sheet_id = result[0]
                elif isinstance(result, (int, float)):
                    sheet_id = result
                else:
                    sheet_id = None
                
                if sheet_id and sheet_id > 0:
                    print(f"\n=== DEBUGGING SHEET INFO (ID: {sheet_id}) ===")
                    try:
                        sheet.SetId(sheet_id)
                        page_number = sheet.GetAssignment()
                        sheet_name = sheet.GetName()
                        print(f"Sheet name: {sheet_name}")
                        print(f"Page number: {page_number}")
                        print(f"Page number type: {type(page_number)}")
                    except Exception as e:
                        print(f"Error getting sheet info: {e}")
                
            except Exception as e:
                print(f"Error processing pin: {e}")
            
        except Exception as e:
            print(f"Error processing connection: {e}")
        
        print("\nDebug completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 API Debug Script")
    print("=" * 30)
    
    debug_e3_api()

if __name__ == "__main__":
    main()
