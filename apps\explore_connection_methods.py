#!/usr/bin/env python3
"""
Explore all available methods on E3 connection objects

This script discovers what methods are available on connection objects
to find alternative ways to set wire numbers.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def explore_connection_methods():
    """Explore all methods available on connection objects"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nExploring connection {test_conn_id}")
        
        connection.SetId(test_conn_id)
        signal_name = connection.GetSignalName()
        print(f"Signal: {signal_name}")
        
        # Get all methods and properties
        print("\n=== Available methods and properties ===")
        
        # Use dir() to get all attributes
        all_attrs = dir(connection)
        
        # Filter for methods that might be relevant
        relevant_methods = []
        for attr in all_attrs:
            if not attr.startswith('_'):  # Skip private attributes
                try:
                    obj = getattr(connection, attr)
                    if callable(obj):
                        relevant_methods.append(attr)
                except:
                    pass
        
        print(f"Found {len(relevant_methods)} public methods:")
        
        # Group methods by likely function
        get_methods = [m for m in relevant_methods if m.startswith('Get')]
        set_methods = [m for m in relevant_methods if m.startswith('Set')]
        wire_methods = [m for m in relevant_methods if 'Wire' in m or 'wire' in m]
        attr_methods = [m for m in relevant_methods if 'Attribute' in m or 'attribute' in m]
        other_methods = [m for m in relevant_methods if m not in get_methods + set_methods + wire_methods + attr_methods]
        
        print(f"\nGet methods ({len(get_methods)}):")
        for method in sorted(get_methods):
            print(f"  {method}")
        
        print(f"\nSet methods ({len(set_methods)}):")
        for method in sorted(set_methods):
            print(f"  {method}")
        
        print(f"\nWire-related methods ({len(wire_methods)}):")
        for method in sorted(wire_methods):
            print(f"  {method}")
        
        print(f"\nAttribute methods ({len(attr_methods)}):")
        for method in sorted(attr_methods):
            print(f"  {method}")
        
        print(f"\nOther methods ({len(other_methods)}):")
        for method in sorted(other_methods):
            print(f"  {method}")
        
        # Test some promising methods
        print("\n=== Testing promising methods ===")
        
        # Test wire-related methods
        for method in wire_methods:
            try:
                func = getattr(connection, method)
                if method.startswith('Get'):
                    result = func()
                    print(f"{method}(): {result}")
                elif method.startswith('Set') and 'Wire' in method:
                    print(f"{method}: Available for setting")
            except Exception as e:
                print(f"{method}: Error - {e}")
        
        # Test if there are methods to add/create attributes
        create_methods = [m for m in relevant_methods if 'Create' in m or 'Add' in m or 'New' in m]
        if create_methods:
            print(f"\nCreate/Add methods ({len(create_methods)}):")
            for method in sorted(create_methods):
                print(f"  {method}")
        
        # Try some specific methods that might work
        print("\n=== Testing specific wire number methods ===")
        
        test_methods = [
            ('SetWireNumber', 'TEST_WIRE_123'),
            ('SetWireName', 'TEST_WIRE_123'),
            ('SetNumber', 'TEST_WIRE_123'),
            ('SetLabel', 'TEST_WIRE_123'),
            ('SetText', 'TEST_WIRE_123')
        ]
        
        for method_name, test_value in test_methods:
            if method_name in relevant_methods:
                try:
                    print(f"\nTrying {method_name}('{test_value}')...")
                    method = getattr(connection, method_name)
                    result = method(test_value)
                    print(f"  Result: {result}")
                    
                    # Try to verify by getting the value back
                    get_method_name = method_name.replace('Set', 'Get')
                    if get_method_name in relevant_methods:
                        get_method = getattr(connection, get_method_name)
                        verify_value = get_method()
                        print(f"  Verification ({get_method_name}): '{verify_value}'")
                        
                        if str(verify_value) == test_value:
                            print(f"  ✓ SUCCESS with {method_name}!")
                        else:
                            print(f"  ✗ Value not set correctly")
                    
                except Exception as e:
                    print(f"  Error: {e}")
            else:
                print(f"{method_name}: Not available")
        
        print("\nMethod exploration completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Connection Method Explorer")
    print("=" * 30)
    
    explore_connection_methods()

if __name__ == "__main__":
    main()
