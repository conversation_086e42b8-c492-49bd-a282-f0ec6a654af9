#!/usr/bin/env python3
"""
Explore wire objects accessible through connections

This script investigates if we can access wire objects through connections
and set wire numbers that way.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def explore_wire_through_connection():
    """Explore wire objects through connections"""
    try:
        # Connect to E3
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        
        print("Successfully connected to E3!")
        
        # Get connection IDs
        connection_ids_result = job.GetAllConnectionIds()
        if not connection_ids_result:
            print("No connections found")
            return
        
        # Parse connection IDs
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            count = connection_ids_result[0]
            connection_ids = connection_ids_result[1]
            
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
        else:
            print(f"Unexpected connection IDs format: {type(connection_ids_result)}")
            return
        
        # Test first connection
        test_conn_id = actual_connections[0]
        print(f"\nExploring connection {test_conn_id}")
        
        connection.SetId(test_conn_id)
        signal_name = connection.GetSignalName()
        print(f"Signal: {signal_name}")
        
        # Try to get wire objects from the connection
        print("\n=== Exploring wire access through connection ===")
        
        # Check if connection has wire-related methods
        wire_methods = [attr for attr in dir(connection) if 'wire' in attr.lower()]
        print(f"Wire-related methods on connection: {wire_methods}")
        
        # Try to get cores (wires) from the connection
        try:
            core_count_result = connection.GetCoreCount()
            print(f"GetCoreCount result: {core_count_result}")
            
            # Handle tuple return
            if isinstance(core_count_result, tuple):
                core_count = core_count_result[0] if len(core_count_result) > 0 else 0
                print(f"Core count from tuple: {core_count}")
            else:
                core_count = core_count_result
                print(f"Core count: {core_count}")
            
            if core_count > 0:
                # Get core IDs
                core_ids_result = connection.GetCoreIds()
                print(f"GetCoreIds result: {core_ids_result}")
                
                # Handle tuple return
                if isinstance(core_ids_result, tuple) and len(core_ids_result) >= 2:
                    core_ids = core_ids_result[1]
                    if isinstance(core_ids, tuple):
                        actual_cores = [cid for cid in core_ids if cid is not None]
                    else:
                        actual_cores = [core_ids] if core_ids is not None else []
                else:
                    actual_cores = []
                
                print(f"Found {len(actual_cores)} cores: {actual_cores}")
                
                # Try to work with core objects
                if actual_cores:
                    core = job.CreateCoreObject()
                    test_core_id = actual_cores[0]
                    print(f"\nTesting core {test_core_id}")
                    
                    try:
                        core.SetId(test_core_id)
                        
                        # Explore core methods
                        core_methods = [attr for attr in dir(core) if not attr.startswith('_')]
                        wire_core_methods = [m for m in core_methods if 'wire' in m.lower() or 'number' in m.lower()]
                        print(f"Wire/number methods on core: {wire_core_methods}")
                        
                        # Try to set wire number on core
                        try:
                            print("Trying SetAttributeValue on core...")
                            core_set_result = core.SetAttributeValue("Wire number", "CORE_TEST_123")
                            print(f"Core SetAttributeValue result: {core_set_result}")
                            
                            core_verify = core.GetAttributeValue("Wire number")
                            print(f"Core wire number after set: '{core_verify}'")
                            
                            if core_verify == "CORE_TEST_123":
                                print("✓ SUCCESS with core SetAttributeValue!")
                            else:
                                print("✗ Core SetAttributeValue didn't work")
                                
                        except Exception as e:
                            print(f"Core SetAttributeValue failed: {e}")
                        
                    except Exception as e:
                        print(f"Error working with core {test_core_id}: {e}")
            
        except Exception as e:
            print(f"Error getting cores: {e}")
        
        # Try to access wire objects directly through job
        print("\n=== Exploring wire objects directly ===")
        try:
            wire = job.CreateWireObject()
            print("Created wire object successfully")
            
            # Try to get all wire IDs
            try:
                wire_ids_result = job.GetAllWireIds()
                print(f"GetAllWireIds result: {wire_ids_result}")
                
                # Handle tuple return
                if isinstance(wire_ids_result, tuple) and len(wire_ids_result) >= 2:
                    wire_count = wire_ids_result[0]
                    wire_ids = wire_ids_result[1]
                    
                    if isinstance(wire_ids, tuple):
                        actual_wires = [wid for wid in wire_ids if wid is not None]
                    else:
                        actual_wires = [wire_ids] if wire_ids is not None else []
                    
                    print(f"Found {len(actual_wires)} wires")
                    
                    if actual_wires:
                        test_wire_id = actual_wires[0]
                        print(f"Testing wire {test_wire_id}")
                        
                        try:
                            wire.SetId(test_wire_id)
                            
                            # Try to set wire number on wire object
                            print("Trying SetAttributeValue on wire...")
                            wire_set_result = wire.SetAttributeValue("Wire number", "WIRE_TEST_123")
                            print(f"Wire SetAttributeValue result: {wire_set_result}")
                            
                            wire_verify = wire.GetAttributeValue("Wire number")
                            print(f"Wire number after set: '{wire_verify}'")
                            
                            if wire_verify == "WIRE_TEST_123":
                                print("✓ SUCCESS with wire SetAttributeValue!")
                            else:
                                print("✗ Wire SetAttributeValue didn't work")
                                
                        except Exception as e:
                            print(f"Error working with wire {test_wire_id}: {e}")
                
            except Exception as e:
                print(f"Error getting wire IDs: {e}")
                
        except Exception as e:
            print(f"Error creating wire object: {e}")
        
        print("\nWire exploration completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure E3 is running with a project open.")

def main():
    """Main function"""
    print("E3 Wire Through Connection Explorer")
    print("=" * 35)
    
    explore_wire_through_connection()

if __name__ == "__main__":
    main()
