#!/usr/bin/env python3
"""
Test E3's wire format and range methods with proper parameters

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_wire_format_methods():
    """Test wire format methods with proper parameters"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Test GetGeneratedWireNameFormat to understand the current format
        print("\n=== Current wire name format ===")
        try:
            current_format = job.GetGeneratedWireNameFormat()
            print(f"GetGeneratedWireNameFormat: {current_format}")
            
            if isinstance(current_format, tuple):
                print(f"Format tuple length: {len(current_format)}")
                for i, item in enumerate(current_format):
                    print(f"  [{i}]: {item} (type: {type(item)})")
        except Exception as e:
            print(f"GetGeneratedWireNameFormat failed: {e}")
        
        # Test GetGeneratedWireNameFormatEx
        try:
            current_format_ex = job.GetGeneratedWireNameFormatEx()
            print(f"GetGeneratedWireNameFormatEx: {current_format_ex}")
        except Exception as e:
            print(f"GetGeneratedWireNameFormatEx failed: {e}")
        
        # Test GetWireRange to understand current range
        print("\n=== Current wire range ===")
        try:
            current_range = job.GetWireRange()
            print(f"GetWireRange: {current_range}")
            
            if isinstance(current_range, tuple):
                print(f"Range tuple length: {len(current_range)}")
                for i, item in enumerate(current_range):
                    print(f"  [{i}]: {item} (type: {type(item)})")
        except Exception as e:
            print(f"GetWireRange failed: {e}")
        
        # Test setting a simple wire format
        print("\n=== Testing SetGeneratedWireNameFormat ===")
        
        # Try different parameter combinations
        format_tests = [
            ("Simple string", "W{0}"),
            ("With page", "{page}({grid})"),
            ("Number only", "{0}"),
            ("Page and position", "{page}({position})"),
        ]
        
        for test_name, format_string in format_tests:
            try:
                print(f"Testing {test_name}: '{format_string}'")
                result = job.SetGeneratedWireNameFormat(format_string)
                print(f"  SetGeneratedWireNameFormat result: {result}")
                
                # Check if it was set
                new_format = job.GetGeneratedWireNameFormat()
                print(f"  New format: {new_format}")
                
                if result != 0 and result > 0:
                    print(f"  ✓ SUCCESS: Format set with result {result}")
                    
                    # Test if this affects wire number generation
                    next_wire = job.GetNextWireNumberFormatted()
                    print(f"  Next formatted wire: '{next_wire}'")
                    
                    break  # Stop on first success
                    
            except Exception as e:
                print(f"  SetGeneratedWireNameFormat failed: {e}")
        
        # Test SetWireRange with different parameters
        print("\n=== Testing SetWireRange ===")
        
        range_tests = [
            ("Range 1-1000", 1, 1000),
            ("Range 0-999", 0, 999),
            ("Range 1-100", 1, 100),
        ]
        
        for test_name, start, end in range_tests:
            try:
                print(f"Testing {test_name}: {start} to {end}")
                result = job.SetWireRange(start, end)
                print(f"  SetWireRange result: {result}")
                
                if result != 0 and result > 0:
                    print(f"  ✓ SUCCESS: Range set with result {result}")
                    
                    # Check new range
                    new_range = job.GetWireRange()
                    print(f"  New range: {new_range}")
                    
                    # Test next wire number
                    next_wire = job.GetNextWireNumber()
                    print(f"  Next wire number: {next_wire}")
                    
                    break  # Stop on first success
                    
            except Exception as e:
                print(f"  SetWireRange failed: {e}")
        
        # Test if we can now assign wire numbers to connections
        print("\n=== Testing wire number assignment after format change ===")
        
        # Get a connection to test with
        connection_ids_result = job.GetAllConnectionIds()
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            connection_ids = connection_ids_result[1]
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
            
            if actual_connections:
                connection = job.CreateConnectionObject()
                test_conn_id = actual_connections[0]
                connection.SetId(test_conn_id)
                
                signal_name = connection.GetSignalName()
                print(f"Testing connection {test_conn_id}, signal: {signal_name}")
                
                # Try to get a wire number from the system
                next_wire = job.GetNextWireNumberFormatted()
                print(f"Next formatted wire number: '{next_wire}'")
                
                # Try to set this wire number on the connection
                set_result = connection.SetAttributeValue("Wire number", next_wire)
                print(f"SetAttributeValue result: {set_result}")
                
                # Verify
                new_wire_number = connection.GetAttributeValue("Wire number")
                print(f"Connection wire number after set: '{new_wire_number}'")
                
                if new_wire_number == next_wire:
                    print("✓ SUCCESS: Wire number assigned to connection!")
                    return True
                else:
                    print("✗ FAILED: Wire number not assigned")
        
        print("\nWire format method test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Wire Format Method Test")
    print("==========================")
    success = test_wire_format_methods()
    if success:
        print("\n✓ Found working method for wire number assignment!")
    else:
        print("\n✗ No working method found for wire number assignment.")
