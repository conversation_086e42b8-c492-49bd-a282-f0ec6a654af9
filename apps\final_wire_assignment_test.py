#!/usr/bin/env python3
"""
Final test - try to find any method that actually assigns wire numbers to connections

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def final_wire_assignment_test():
    """Final test for wire number assignment"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Look for any methods that might assign wire numbers
        job_methods = [attr for attr in dir(job) if not attr.startswith('_')]
        
        # Look for methods containing assign, apply, set, create, generate, auto
        assignment_keywords = ['assign', 'apply', 'generate', 'auto', 'create', 'number']
        potential_methods = []
        
        for keyword in assignment_keywords:
            methods = [m for m in job_methods if keyword.lower() in m.lower()]
            potential_methods.extend(methods)
        
        # Remove duplicates and sort
        potential_methods = sorted(list(set(potential_methods)))
        
        print(f"\n=== Potential wire assignment methods ({len(potential_methods)}) ===")
        for method in potential_methods:
            print(f"  {method}")
        
        # Test some promising methods
        promising_methods = [
            'AutoGenerateWireNumbers',
            'GenerateWireNumbers', 
            'AssignWireNumbers',
            'ApplyWireNumbers',
            'CreateWireNumbers',
            'SetWireNumbers',
            'NumberWires',
            'AutoNumber',
            'GenerateNumbers'
        ]
        
        print(f"\n=== Testing promising methods ===")
        for method in promising_methods:
            if method in job_methods:
                try:
                    print(f"Testing {method}...")
                    result = getattr(job, method)()
                    print(f"  {method} result: {result}")
                    
                    # Check if this affected any connections
                    connection = job.CreateConnectionObject()
                    connection_ids_result = job.GetAllConnectionIds()
                    
                    if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
                        connection_ids = connection_ids_result[1]
                        if isinstance(connection_ids, tuple):
                            test_conn_id = connection_ids[0] if connection_ids[0] is not None else connection_ids[1]
                        else:
                            test_conn_id = connection_ids
                        
                        if test_conn_id:
                            connection.SetId(test_conn_id)
                            wire_number = connection.GetAttributeValue("Wire number")
                            print(f"  Connection {test_conn_id} wire number after {method}: '{wire_number}'")
                            
                            if wire_number and wire_number != "":
                                print(f"  ✓ SUCCESS: {method} assigned wire numbers!")
                                return True
                    
                except Exception as e:
                    print(f"  {method} failed: {e}")
            else:
                print(f"  {method} - not available")
        
        # Try connection-specific methods
        print(f"\n=== Testing connection object methods ===")
        connection = job.CreateConnectionObject()
        connection_ids_result = job.GetAllConnectionIds()
        
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            connection_ids = connection_ids_result[1]
            if isinstance(connection_ids, tuple):
                test_conn_id = connection_ids[0] if connection_ids[0] is not None else connection_ids[1]
            else:
                test_conn_id = connection_ids
            
            if test_conn_id:
                connection.SetId(test_conn_id)
                
                # Look for methods on connection object
                conn_methods = [attr for attr in dir(connection) if not attr.startswith('_')]
                wire_conn_methods = [m for m in conn_methods if 'wire' in m.lower() or 'number' in m.lower()]
                
                print(f"Wire/number methods on connection: {wire_conn_methods}")
                
                # Try any promising connection methods
                for method in wire_conn_methods:
                    if method.startswith('Set') or method.startswith('Apply') or method.startswith('Assign'):
                        try:
                            print(f"Testing connection.{method}...")
                            
                            # Try with different parameter types
                            for param in ["1", 1, "TEST_WIRE"]:
                                try:
                                    result = getattr(connection, method)(param)
                                    print(f"  {method}({param}) result: {result}")
                                    
                                    # Check if it worked
                                    wire_number = connection.GetAttributeValue("Wire number")
                                    if wire_number and wire_number != "":
                                        print(f"  ✓ SUCCESS: {method} worked with parameter {param}!")
                                        return True
                                    
                                except Exception as e:
                                    pass  # Try next parameter
                                    
                        except Exception as e:
                            print(f"  {method} failed: {e}")
        
        # Final attempt - maybe wire numbers are read-only and we need to check how they're actually displayed
        print(f"\n=== Final check - examining existing wire numbers ===")
        
        # Check if any connections already have wire numbers
        connection = job.CreateConnectionObject()
        connection_ids_result = job.GetAllConnectionIds()
        
        if isinstance(connection_ids_result, tuple) and len(connection_ids_result) >= 2:
            connection_ids = connection_ids_result[1]
            if isinstance(connection_ids, tuple):
                actual_connections = [cid for cid in connection_ids if cid is not None]
            else:
                actual_connections = [connection_ids] if connection_ids is not None else []
            
            wire_numbers_found = []
            
            # Check first 20 connections
            for conn_id in actual_connections[:20]:
                try:
                    connection.SetId(conn_id)
                    wire_number = connection.GetAttributeValue("Wire number")
                    signal_name = connection.GetSignalName()
                    
                    if wire_number and wire_number.strip() != "":
                        wire_numbers_found.append((conn_id, signal_name, wire_number))
                        print(f"  Connection {conn_id} (signal '{signal_name}') has wire number: '{wire_number}'")
                        
                except Exception as e:
                    pass
            
            if wire_numbers_found:
                print(f"\n✓ Found {len(wire_numbers_found)} connections with existing wire numbers!")
                print("This suggests wire numbers can be stored, but we haven't found the right method to set them.")
            else:
                print("\n✗ No connections found with wire numbers.")
                print("This suggests either:")
                print("  1. Wire numbers aren't stored as attributes")
                print("  2. Wire numbers are managed through a different system")
                print("  3. Wire numbers need to be enabled/configured first")
        
        print("\nFinal wire assignment test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Final Wire Assignment Test")
    print("=============================")
    success = final_wire_assignment_test()
    if success:
        print("\n✓ Found working method for wire number assignment!")
    else:
        print("\n✗ No working method found. Wire numbers may not be supported in this E3 configuration.")
