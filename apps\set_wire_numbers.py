#!/usr/bin/env python3
"""
E3 Wire Number Assignment Script

This script reads through the current open E3 project and for every connection,
sets the "Wire number" to a calculated value based on page number and grid/ladder position.
The wire number format is: f"{page_number}({grid_position})"
The end that results in the lowest wire number is used.
All connections with the same signal get the same wire number.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import logging
import sys
from collections import defaultdict
import re

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wire_numbering.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class WireNumberAssigner:
    def __init__(self):
        self.app = None
        self.job = None
        self.connection = None
        self.pin = None
        self.sheet = None
        
    def connect_to_e3(self):
        """Connect to the open E3 application"""
        try:
            self.app = win32com.client.GetActiveObject("CT.Application")
            self.job = self.app.CreateJobObject()
            self.connection = self.job.CreateConnectionObject()
            self.pin = self.job.CreatePinObject()
            self.sheet = self.job.CreateSheetObject()
            logging.info("Successfully connected to E3 application")
            return True
        except Exception as e:
            logging.error(f"Failed to connect to E3: {e}")
            return False
    
    def get_pin_location_info(self, pin_id):
        """Get location information for a pin"""
        try:
            self.pin.SetId(pin_id)

            # Get schema location - this method uses output parameters
            # We need to call it to get the sheet ID and location info
            import pythoncom

            # Create variant objects for output parameters
            x = pythoncom.Empty
            y = pythoncom.Empty
            grid_desc = pythoncom.Empty
            column = pythoncom.Empty
            row = pythoncom.Empty

            # Call GetSchemaLocation with output parameters
            sheet_id = self.pin.GetSchemaLocation(x, y, grid_desc, column, row)

            if sheet_id <= 0:
                logging.warning(f"Pin {pin_id} has no schema location")
                return None, None, None

            # Get sheet assignment (page number)
            self.sheet.SetId(sheet_id)
            page_number = self.sheet.GetAssignment()

            # Extract grid position from grid_desc or use column/row
            grid_position = self.extract_grid_position(grid_desc, column, row)

            return page_number, grid_position, sheet_id

        except Exception as e:
            logging.error(f"Error getting pin location for pin {pin_id}: {e}")
            return None, None, None
    
    def extract_grid_position(self, grid_desc, column, row):
        """Extract grid position from grid description or column/row"""
        try:
            # If we have grid_desc in format "/sheet.grid", extract the grid part
            if grid_desc and "." in grid_desc:
                grid_part = grid_desc.split(".")[-1]
                return grid_part
            
            # If we have column and row, combine them
            if column and row:
                return f"{column}{row}"
            
            # Fallback to just column or row if available
            if column:
                return column
            if row:
                return row
                
            return "UNKNOWN"
            
        except Exception as e:
            logging.error(f"Error extracting grid position: {e}")
            return "UNKNOWN"
    
    def calculate_wire_number(self, page_number, grid_position):
        """Calculate wire number from page and grid position"""
        try:
            # Handle empty or None page numbers
            if not page_number or page_number.strip() == "":
                page_num = "0"
            else:
                page_num = str(page_number).strip()
            
            # Format: page_number(grid_position)
            wire_number = f"{page_num}({grid_position})"
            return wire_number
            
        except Exception as e:
            logging.error(f"Error calculating wire number: {e}")
            return "ERROR"
    
    def get_connection_wire_numbers(self, connection_id):
        """Get potential wire numbers for both ends of a connection"""
        try:
            self.connection.SetId(connection_id)
            
            # Get pin IDs for this connection
            pin_ids = self.connection.GetPinIds()
            if not pin_ids:
                logging.warning(f"Connection {connection_id} has no pins")
                return []
            
            wire_numbers = []
            
            for pin_id in pin_ids:
                if pin_id is None:
                    continue
                    
                page_number, grid_position, sheet_id = self.get_pin_location_info(pin_id)
                if page_number is not None and grid_position is not None:
                    wire_number = self.calculate_wire_number(page_number, grid_position)
                    wire_numbers.append(wire_number)
                    logging.debug(f"Pin {pin_id}: Page {page_number}, Grid {grid_position} -> Wire {wire_number}")
            
            return wire_numbers
            
        except Exception as e:
            logging.error(f"Error getting wire numbers for connection {connection_id}: {e}")
            return []
    
    def get_lowest_wire_number(self, wire_numbers):
        """Get the lowest wire number from a list"""
        if not wire_numbers:
            return None
        
        # Sort wire numbers to get the lowest one
        # This will sort lexicographically, which should work for most cases
        sorted_numbers = sorted(wire_numbers)
        return sorted_numbers[0]
    
    def process_connections(self):
        """Process all connections in the project"""
        try:
            # Get all connection IDs
            connection_ids = self.job.GetAllConnectionIds()
            if not connection_ids:
                logging.warning("No connections found in project")
                return
            
            logging.info(f"Found {len(connection_ids)} connections to process")
            
            # Group connections by signal name
            signal_groups = defaultdict(list)
            connection_wire_numbers = {}
            
            # First pass: calculate wire numbers for each connection
            for conn_id in connection_ids:
                if conn_id is None:
                    continue
                    
                try:
                    self.connection.SetId(conn_id)
                    signal_name = self.connection.GetSignalName()
                    
                    # Get potential wire numbers for this connection
                    wire_numbers = self.get_connection_wire_numbers(conn_id)
                    lowest_wire_number = self.get_lowest_wire_number(wire_numbers)
                    
                    if lowest_wire_number:
                        connection_wire_numbers[conn_id] = lowest_wire_number
                        signal_groups[signal_name].append((conn_id, lowest_wire_number))
                        logging.debug(f"Connection {conn_id} (Signal: {signal_name}) -> Wire: {lowest_wire_number}")
                    else:
                        logging.warning(f"Could not calculate wire number for connection {conn_id}")
                        
                except Exception as e:
                    logging.error(f"Error processing connection {conn_id}: {e}")
            
            # Second pass: determine the lowest wire number for each signal group
            signal_wire_numbers = {}
            for signal_name, connections in signal_groups.items():
                if connections:
                    # Get all wire numbers for this signal
                    wire_numbers = [wire_num for _, wire_num in connections]
                    lowest_wire_number = self.get_lowest_wire_number(wire_numbers)
                    signal_wire_numbers[signal_name] = lowest_wire_number
                    logging.info(f"Signal '{signal_name}' assigned wire number: {lowest_wire_number}")
            
            # Third pass: set wire numbers on all connections
            updated_count = 0
            for signal_name, connections in signal_groups.items():
                wire_number = signal_wire_numbers.get(signal_name)
                if wire_number:
                    for conn_id, _ in connections:
                        try:
                            self.connection.SetId(conn_id)
                            # Try common wire number attribute names
                            for attr_name in ["Wire number", "WIRE_NUMBER", "WireNumber", "Wire_Number"]:
                                try:
                                    result = self.connection.SetAttributeValue(attr_name, wire_number)
                                    if result > 0:  # Success
                                        logging.info(f"Set {attr_name}='{wire_number}' for connection {conn_id}")
                                        updated_count += 1
                                        break
                                except Exception as e:
                                    logging.debug(f"Failed to set {attr_name} for connection {conn_id}: {e}")
                                    continue
                            else:
                                logging.warning(f"Could not set wire number for connection {conn_id} - no valid attribute found")
                                
                        except Exception as e:
                            logging.error(f"Error setting wire number for connection {conn_id}: {e}")
            
            logging.info(f"Successfully updated wire numbers for {updated_count} connections")
            
        except Exception as e:
            logging.error(f"Error processing connections: {e}")
    
    def run(self):
        """Main execution method"""
        logging.info("Starting wire number assignment process")
        
        if not self.connect_to_e3():
            logging.error("Failed to connect to E3. Make sure E3 is running with a project open.")
            return False
        
        try:
            self.process_connections()
            logging.info("Wire number assignment completed successfully")
            return True
            
        except Exception as e:
            logging.error(f"Error during wire number assignment: {e}")
            return False
        
        finally:
            # Clean up COM objects
            self.app = None
            self.job = None
            self.connection = None
            self.pin = None
            self.sheet = None

def main():
    """Main function"""
    assigner = WireNumberAssigner()
    success = assigner.run()
    
    if success:
        print("Wire number assignment completed successfully!")
        print("Check the log file 'wire_numbering.log' for details.")
    else:
        print("Wire number assignment failed. Check the log file for errors.")
        sys.exit(1)

if __name__ == "__main__":
    main()
