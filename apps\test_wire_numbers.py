#!/usr/bin/env python3
"""
Test script for E3 Wire Number Assignment

This script tests the wire numbering functionality by connecting to E3
and displaying connection information without making changes.

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import logging
import sys

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

def test_e3_connection():
    """Test connection to E3 and display basic project info"""
    try:
        # Connect to E3
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        connection = job.CreateConnectionObject()
        pin = job.CreatePinObject()
        sheet = job.CreateSheetObject()
        
        print("Successfully connected to E3!")
        
        # Get project info
        project_name = job.GetName()
        print(f"Project Name: {project_name}")
        
        # Get connection count
        connection_ids = job.GetAllConnectionIds()
        print(f"Total Connections: {len(connection_ids) if connection_ids else 0}")
        
        # Test first few connections
        if connection_ids:
            print("\nTesting first 5 connections:")
            for i, conn_id in enumerate(connection_ids[:5]):
                if conn_id is None:
                    continue
                    
                try:
                    connection.SetId(conn_id)
                    signal_name = connection.GetSignalName()
                    
                    # Get pin IDs
                    pin_ids = connection.GetPinIds()
                    
                    print(f"\nConnection {conn_id}:")
                    print(f"  Signal: {signal_name}")
                    print(f"  Pin Count: {len(pin_ids) if pin_ids else 0}")
                    
                    # Test pin locations
                    if pin_ids:
                        for j, pin_id in enumerate(pin_ids[:2]):  # Test first 2 pins
                            if pin_id is None:
                                continue
                                
                            try:
                                pin.SetId(pin_id)
                                
                                # Try to get schema location
                                import pythoncom
                                x = pythoncom.Empty
                                y = pythoncom.Empty
                                grid_desc = pythoncom.Empty
                                column = pythoncom.Empty
                                row = pythoncom.Empty
                                
                                sheet_id = pin.GetSchemaLocation(x, y, grid_desc, column, row)
                                
                                if sheet_id > 0:
                                    sheet.SetId(sheet_id)
                                    page_number = sheet.GetAssignment()
                                    sheet_name = sheet.GetName()
                                    
                                    print(f"    Pin {pin_id}:")
                                    print(f"      Sheet: {sheet_name} (ID: {sheet_id})")
                                    print(f"      Page: {page_number}")
                                    print(f"      Grid: {grid_desc}")
                                    print(f"      Column: {column}")
                                    print(f"      Row: {row}")
                                    
                                    # Calculate potential wire number
                                    if grid_desc and "." in str(grid_desc):
                                        grid_part = str(grid_desc).split(".")[-1]
                                    elif column and row:
                                        grid_part = f"{column}{row}"
                                    elif column:
                                        grid_part = str(column)
                                    elif row:
                                        grid_part = str(row)
                                    else:
                                        grid_part = "UNKNOWN"
                                    
                                    page_num = str(page_number).strip() if page_number else "0"
                                    wire_number = f"{page_num}({grid_part})"
                                    print(f"      Calculated Wire Number: {wire_number}")
                                else:
                                    print(f"    Pin {pin_id}: No schema location")
                                    
                            except Exception as e:
                                print(f"    Error processing pin {pin_id}: {e}")
                    
                except Exception as e:
                    print(f"Error processing connection {conn_id}: {e}")
        
        # Test attribute setting (without actually setting)
        print("\nTesting attribute access...")
        if connection_ids:
            conn_id = connection_ids[0]
            if conn_id is not None:
                try:
                    connection.SetId(conn_id)
                    
                    # Try to get existing wire number attribute
                    for attr_name in ["Wire number", "WIRE_NUMBER", "WireNumber", "Wire_Number"]:
                        try:
                            # Note: We're not actually setting, just testing if the attribute exists
                            print(f"  Testing attribute: {attr_name}")
                            # In a real scenario, we would use SetAttributeValue here
                            
                        except Exception as e:
                            print(f"  {attr_name}: {e}")
                            
                except Exception as e:
                    print(f"Error testing attributes: {e}")
        
        print("\nTest completed successfully!")
        return True
        
    except Exception as e:
        print(f"Error connecting to E3: {e}")
        print("Make sure E3 is running with a project open.")
        return False

def main():
    """Main function"""
    print("E3 Wire Number Assignment - Test Script")
    print("=" * 50)
    
    success = test_e3_connection()
    
    if success:
        print("\nTest completed successfully!")
        print("You can now run the main wire numbering script: set_wire_numbers.py")
    else:
        print("\nTest failed. Please check E3 connection and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
