#!/usr/bin/env python3
"""
Simple test to see if we can access wire objects and set wire numbers

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_simple_wire():
    """Simple wire test"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Try to create a wire object
        try:
            wire = job.CreateWireObject()
            print("Wire object created successfully")
            
            # Try to get wire IDs
            wire_ids_result = job.GetAllWireIds()
            print(f"GetAllWireIds result type: {type(wire_ids_result)}")
            print(f"GetAllWireIds result: {wire_ids_result}")
            
            # Handle the result properly
            if isinstance(wire_ids_result, tuple) and len(wire_ids_result) >= 2:
                wire_count = wire_ids_result[0]
                wire_ids = wire_ids_result[1]
                print(f"Wire count: {wire_count}")
                print(f"Wire IDs type: {type(wire_ids)}")
                
                if isinstance(wire_ids, tuple):
                    actual_wires = [wid for wid in wire_ids if wid is not None]
                else:
                    actual_wires = [wire_ids] if wire_ids is not None else []
                
                print(f"Found {len(actual_wires)} wires")
                
                if actual_wires:
                    test_wire_id = actual_wires[0]
                    print(f"Testing wire ID: {test_wire_id}")
                    
                    wire.SetId(test_wire_id)
                    
                    # Try to get current wire number
                    current_wire_number = wire.GetAttributeValue("Wire number")
                    print(f"Current wire number: '{current_wire_number}'")
                    
                    # Try to set wire number
                    set_result = wire.SetAttributeValue("Wire number", "TEST_WIRE_123")
                    print(f"SetAttributeValue result: {set_result}")
                    
                    # Verify
                    new_wire_number = wire.GetAttributeValue("Wire number")
                    print(f"New wire number: '{new_wire_number}'")
                    
                    if new_wire_number == "TEST_WIRE_123":
                        print("✓ SUCCESS: Wire number set on wire object!")
                    else:
                        print("✗ FAILED: Wire number not set")
                else:
                    print("No wire IDs found")
            else:
                print(f"Unexpected wire IDs format: {wire_ids_result}")
                
        except Exception as e:
            print(f"Wire object test failed: {e}")
            import traceback
            traceback.print_exc()
        
        print("Test completed!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Simple Wire Test")
    print("================")
    test_simple_wire()
