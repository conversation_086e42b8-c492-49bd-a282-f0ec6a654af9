#!/usr/bin/env python3
"""
Test different ways to access cores and set wire numbers

Author: Assistant
Date: 2025-01-01
"""

import win32com.client
import sys

def test_core_access():
    """Test core access methods"""
    try:
        print("Connecting to E3...")
        app = win32com.client.GetActiveObject("CT.Application")
        job = app.CreateJobObject()
        
        print("Successfully connected to E3!")
        
        # Check if there are any methods to get core objects directly
        print("\n=== Looking for core-related methods ===")
        
        # Get new core IDs - this might give us cores that were recently created
        try:
            new_core_ids = job.GetNewCoreIds()
            print(f"GetNewCoreIds: {new_core_ids}")
            
            if isinstance(new_core_ids, tuple):
                print(f"GetNewCoreIds returned tuple: {new_core_ids}")
        except Exception as e:
            print(f"GetNewCoreIds failed: {e}")
        
        # Try to see if we can work with net segments instead
        print("\n=== Testing net segments ===")
        try:
            net_segment = job.CreateNetSegmentObject()
            print("NetSegment object created successfully")
            
            # Get all net segment IDs
            net_segment_ids_result = job.GetAllNetSegmentIds()
            print(f"GetAllNetSegmentIds result: {net_segment_ids_result}")
            
            # Handle tuple return
            if isinstance(net_segment_ids_result, tuple) and len(net_segment_ids_result) >= 2:
                net_count = net_segment_ids_result[0]
                net_segment_ids = net_segment_ids_result[1]
                
                if isinstance(net_segment_ids, tuple):
                    actual_nets = [nid for nid in net_segment_ids if nid is not None]
                else:
                    actual_nets = [net_segment_ids] if net_segment_ids is not None else []
                
                print(f"Found {len(actual_nets)} net segments")
                
                if actual_nets:
                    test_net_id = actual_nets[0]
                    print(f"Testing net segment {test_net_id}")
                    
                    net_segment.SetId(test_net_id)
                    
                    # Try to get/set wire number on net segment
                    net_wire_number = net_segment.GetAttributeValue("Wire number")
                    print(f"Net segment wire number: '{net_wire_number}'")
                    
                    # Try to set wire number
                    test_wire_num = "NET_TEST_123"
                    net_set_result = net_segment.SetAttributeValue("Wire number", test_wire_num)
                    print(f"Net segment SetAttributeValue result: {net_set_result}")
                    
                    # Verify
                    new_net_wire_number = net_segment.GetAttributeValue("Wire number")
                    print(f"New net segment wire number: '{new_net_wire_number}'")
                    
                    if new_net_wire_number == test_wire_num:
                        print("✓ SUCCESS: Wire number set on net segment!")
                        return True
                    else:
                        print("✗ FAILED: Wire number not set on net segment")
            
        except Exception as e:
            print(f"Net segment test failed: {e}")
        
        # Try to see if we can work with signals
        print("\n=== Testing signals ===")
        try:
            signal = job.CreateSignalObject()
            print("Signal object created successfully")
            
            # Get all signal IDs
            signal_ids_result = job.GetAllSignalIds()
            print(f"GetAllSignalIds result: {signal_ids_result}")
            
            # Handle tuple return
            if isinstance(signal_ids_result, tuple) and len(signal_ids_result) >= 2:
                signal_count = signal_ids_result[0]
                signal_ids = signal_ids_result[1]
                
                if isinstance(signal_ids, tuple):
                    actual_signals = [sid for sid in signal_ids if sid is not None]
                else:
                    actual_signals = [signal_ids] if signal_ids is not None else []
                
                print(f"Found {len(actual_signals)} signals")
                
                if actual_signals:
                    test_signal_id = actual_signals[0]
                    print(f"Testing signal {test_signal_id}")
                    
                    signal.SetId(test_signal_id)
                    
                    # Get signal name
                    signal_name = signal.GetName()
                    print(f"Signal name: {signal_name}")
                    
                    # Try to get/set wire number on signal
                    signal_wire_number = signal.GetAttributeValue("Wire number")
                    print(f"Signal wire number: '{signal_wire_number}'")
                    
                    # Try to set wire number
                    test_wire_num = "SIGNAL_TEST_123"
                    signal_set_result = signal.SetAttributeValue("Wire number", test_wire_num)
                    print(f"Signal SetAttributeValue result: {signal_set_result}")
                    
                    # Verify
                    new_signal_wire_number = signal.GetAttributeValue("Wire number")
                    print(f"New signal wire number: '{new_signal_wire_number}'")
                    
                    if new_signal_wire_number == test_wire_num:
                        print("✓ SUCCESS: Wire number set on signal!")
                        return True
                    else:
                        print("✗ FAILED: Wire number not set on signal")
            
        except Exception as e:
            print(f"Signal test failed: {e}")
        
        # Try bundle objects
        print("\n=== Testing bundles ===")
        try:
            bundle = job.CreateBundleObject()
            print("Bundle object created successfully")
            
            # Get all bundle IDs
            bundle_ids_result = job.GetAllBundleIds()
            print(f"GetAllBundleIds result: {bundle_ids_result}")
            
            # Handle tuple return
            if isinstance(bundle_ids_result, tuple) and len(bundle_ids_result) >= 2:
                bundle_count = bundle_ids_result[0]
                bundle_ids = bundle_ids_result[1]
                
                if isinstance(bundle_ids, tuple):
                    actual_bundles = [bid for bid in bundle_ids if bid is not None]
                else:
                    actual_bundles = [bundle_ids] if bundle_ids is not None else []
                
                print(f"Found {len(actual_bundles)} bundles")
                
                if actual_bundles:
                    test_bundle_id = actual_bundles[0]
                    print(f"Testing bundle {test_bundle_id}")
                    
                    bundle.SetId(test_bundle_id)
                    
                    # Try to get/set wire number on bundle
                    bundle_wire_number = bundle.GetAttributeValue("Wire number")
                    print(f"Bundle wire number: '{bundle_wire_number}'")
                    
                    # Try to set wire number
                    test_wire_num = "BUNDLE_TEST_123"
                    bundle_set_result = bundle.SetAttributeValue("Wire number", test_wire_num)
                    print(f"Bundle SetAttributeValue result: {bundle_set_result}")
                    
                    # Verify
                    new_bundle_wire_number = bundle.GetAttributeValue("Wire number")
                    print(f"New bundle wire number: '{new_bundle_wire_number}'")
                    
                    if new_bundle_wire_number == test_wire_num:
                        print("✓ SUCCESS: Wire number set on bundle!")
                        return True
                    else:
                        print("✗ FAILED: Wire number not set on bundle")
            
        except Exception as e:
            print(f"Bundle test failed: {e}")
        
        print("\nCore access test completed!")
        return False
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("E3 Core Access Test")
    print("===================")
    success = test_core_access()
    if success:
        print("\n✓ Found a working method for setting wire numbers!")
    else:
        print("\n✗ No working method found yet.")
